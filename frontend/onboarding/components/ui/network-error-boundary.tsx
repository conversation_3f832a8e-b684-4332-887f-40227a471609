"use client"

import React from 'react';
import { ErrorMessage } from './error-message';

interface NetworkErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface NetworkErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class NetworkErrorBoundary extends React.Component<
  NetworkErrorBoundaryProps,
  NetworkErrorBoundaryState
> {
  constructor(props: NetworkErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): NetworkErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Network Error Boundary caught an error:', error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props;
      
      if (Fallback && this.state.error) {
        return <Fallback error={this.state.error} retry={this.retry} />;
      }

      // Default error UI for network/timeout errors
      const isNetworkError = this.state.error?.message?.includes('timeout') || 
                            this.state.error?.message?.includes('fetch failed') ||
                            this.state.error?.message?.includes('ERR_SOCKET_CONNECTION_TIMEOUT');

      return (
        <ErrorMessage
          title={isNetworkError ? "Connection Timeout" : "Something went wrong"}
          message={
            isNetworkError 
              ? "We're having trouble connecting to our servers. Please check your internet connection and try again."
              : "An unexpected error occurred. Please try again."
          }
          actionText="Try Again"
          onAction={this.retry}
        />
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export function useNetworkErrorHandler() {
  const handleError = (error: Error) => {
    if (error.message?.includes('timeout') || 
        error.message?.includes('fetch failed') ||
        error.message?.includes('ERR_SOCKET_CONNECTION_TIMEOUT')) {
      console.error('Network timeout error:', error);
      // You could also show a toast notification here
      return {
        isNetworkError: true,
        userMessage: "Connection timeout. Please check your internet connection and try again."
      };
    }
    
    return {
      isNetworkError: false,
      userMessage: "An unexpected error occurred. Please try again."
    };
  };

  return { handleError };
}
