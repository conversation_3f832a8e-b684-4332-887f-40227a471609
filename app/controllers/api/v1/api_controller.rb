module Api
  module V1
    class ApiController < ApplicationController
      include ExceptionHandler
      include Pagy::Backend

      # Request from trusted applications e.g. <PERSON><PERSON><PERSON>
      def authorize_trusted_request
        token = extract_token_from_header
        return true if TokenService.verify_token_scope(token, 'chatbot', 'ChatBot')
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
      
      # Authorize admin access (for admin dashboard and administrative endpoints)
      def authorize_admin
        token = extract_token_from_header
        
        # Check for admin token
        return true if TokenService.verify_token_scope(token, 'admin', 'Admin')
        
        render json: { error: 'Unauthorized: Admin access required' }, status: :unauthorized
      end

      # Authorize onboarding access (for onboarding flow)
      def authorize_onboarding(resource_owner_id = nil)
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Starting authorization check"
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Resource owner ID: #{resource_owner_id}"
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Request headers: #{request.headers.to_h.select { |k, v| k.downcase.include?('auth') || k.downcase.include?('token') }}"
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Request params: #{params.to_unsafe_h.slice('access_token', 'token')}"

        token = extract_token_from_header
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Extracted token: #{token.present? ? "#{token[0..10]}..." : 'nil'}"

        if token.blank?
          Rails.logger.warn "🔐 [AUTHORIZE_ONBOARDING] ❌ No token found in headers or params"
          render json: { error: 'Unauthorized: No token provided' }, status: :unauthorized
          return false
        end

        verification_result = TokenService.verify_onboarding_jwt(token)
        Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] Token verification result: #{verification_result}"

        if verification_result.present?
          # verification_result contains the energy switch ID
          Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] ✅ Token verified for energy switch: #{verification_result}"

          # If resource_owner_id is provided, verify the token belongs to the correct user
          if resource_owner_id.present?
            # Get the energy switch to check the user ID
            begin
              energy_switch = EnergySwitch.find(verification_result)
              if energy_switch.switch_user_id == resource_owner_id
                Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] ✅ User ID matches: #{resource_owner_id}"
                return true
              else
                Rails.logger.warn "🔐 [AUTHORIZE_ONBOARDING] ❌ User ID mismatch. Token user: #{energy_switch.switch_user_id}, Expected: #{resource_owner_id}"
                render json: { error: 'Unauthorized: Token does not belong to this user' }, status: :unauthorized
                return false
              end
            rescue ActiveRecord::RecordNotFound
              Rails.logger.warn "🔐 [AUTHORIZE_ONBOARDING] ❌ Energy switch not found: #{verification_result}"
              render json: { error: 'Unauthorized: Invalid energy switch' }, status: :unauthorized
              return false
            end
          else
            Rails.logger.info "🔐 [AUTHORIZE_ONBOARDING] ✅ Authorization successful (no user ID check required)"
            return true
          end
        else
          Rails.logger.warn "🔐 [AUTHORIZE_ONBOARDING] ❌ Token verification failed"
          render json: { error: 'Unauthorized: Invalid onboarding token' }, status: :unauthorized
          return false
        end
      end
      
      # Authorize chatbot access (for chatbot-specific endpoints)
      def authorize_chatbot
        token = extract_token_from_header
        return true if TokenService.verify_token_scope(token, 'chatbot', 'ChatBot')
        render json: { error: 'Unauthorized: ChatBot access required' }, status: :unauthorized
      end
    
      def json_response(object, status = :ok)
        return head(status == :ok ? :no_content : status) if object.nil?
    
        render(json: object, status: status)
      end
    
      def unprocessable_entity(errors = [])
        json_response(
          {
            errors: errors.is_a?(Hash) ? [errors] : errors,
          },
          :unprocessable_entity
        )
      end
    
      def handle_unauthorized
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    
      private
    
      def extract_token_from_header
        Rails.logger.debug "🔍 [EXTRACT_TOKEN] Starting token extraction"

        # First check Authorization header
        auth_header = request.headers['Authorization']
        Rails.logger.debug "🔍 [EXTRACT_TOKEN] Authorization header: #{auth_header.present? ? "#{auth_header[0..20]}..." : 'nil'}"

        if auth_header.present?
          # Try Bearer token format
          if auth_header.start_with?('Bearer ')
            token = auth_header.split(' ').last
            Rails.logger.debug "🔍 [EXTRACT_TOKEN] Found Bearer token: #{token[0..10]}..."
            return token
          end

          # If it doesn't start with 'Bearer ', maybe the token is sent directly
          if auth_header !~ /\s/ # No whitespace means it's probably a raw token
            Rails.logger.debug "🔍 [EXTRACT_TOKEN] Found raw token in Authorization header: #{auth_header[0..10]}..."
            return auth_header
          end
        end

        # Check token header (some clients use this instead of Authorization)
        token_header = request.headers['Token'] || request.headers['Access-Token'] || request.headers['AccessToken']
        Rails.logger.debug "🔍 [EXTRACT_TOKEN] Token headers: Token=#{request.headers['Token']}, Access-Token=#{request.headers['Access-Token']}, AccessToken=#{request.headers['AccessToken']}"
        if token_header.present?
          Rails.logger.debug "🔍 [EXTRACT_TOKEN] Found token in custom header: #{token_header[0..10]}..."
          return token_header
        end

        # Then check URL parameter
        url_token = params['access_token'] || params['token']
        Rails.logger.debug "🔍 [EXTRACT_TOKEN] URL params: access_token=#{params['access_token']}, token=#{params['token']}"
        if url_token.present?
          Rails.logger.debug "🔍 [EXTRACT_TOKEN] Found token in URL params: #{url_token[0..10]}..."
          return url_token
        end

        Rails.logger.debug "🔍 [EXTRACT_TOKEN] No token found anywhere"
        nil
      end
    end
  end
end
