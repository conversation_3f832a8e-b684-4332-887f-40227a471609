import { useState, useMemo, useRef } from 'react'
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { But<PERSON> } from '@/components/ui/button'
import { Mail, X, Star, Info, Check, Upload, PhoneCall, Lightbulb, Loader2 } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from '@tanstack/react-router'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table'
import OfferDetailsTabs from './offer_details_tabs'
import { Deal, EnergySwitch } from '@/types/types'
import { Header } from '../header'
import { Footer } from '../footer'
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from "@/components/ui/tooltip"
import { Skeleton } from '@/components/ui/skeleton'

// Theme colors
const THEME = {
  primary: "#fe6232",
  primaryHover: "#fe6232/90",
  secondary: "#FAFAFA",
  success: "#10b981",
  warning: "#f59e0b",
  info: "#3b82f6",
  error: "#ef4444",
}

interface Props {
  energySwitch: EnergySwitch;
}

export function EnergyResults({ energySwitch }: Props) {
  const [selectedTariffType, setSelectedTariffType] = useState("all")
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null)
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)
  const [isCalculationLightboxOpen, setIsCalculationLightboxOpen] = useState(false)
  const [loadingDealId, setLoadingDealId] = useState<string | null>(null)
  const bestDealRef = useRef<HTMLDivElement>(null)
  const router = useRouter();

  const { availableFuelTypes, initialFuelType } = useMemo(() => {
    const types = new Set<"gas" | "electricity">();
    energySwitch.available_tariffs.forEach(tariff => {
      const fuelKeys = Object.keys(tariff.estimated_cost);
      if (fuelKeys.includes("gas")) types.add("gas");
      if (fuelKeys.includes("electricity")) types.add("electricity");
    });
    const fuelTypes = Array.from(types);
    const initial = fuelTypes.includes("gas") ? "gas" : "electricity";
    return { availableFuelTypes: fuelTypes, initialFuelType: initial };
  }, [energySwitch.available_tariffs]);

  const [selectedFuelType, setSelectedFuelType] = useState<"gas" | "electricity">(
    initialFuelType as "gas" | "electricity" || "electricity" // Cast to fix type error
  );

  const filteredTariffs = useMemo(() => {
    if (selectedTariffType === "all") {
      return energySwitch.available_tariffs
        .slice()
        .sort((a, b) => {
          const aSaving = parseFloat(a.estimated_saving["total"]?.amount || "0");
          const bSaving = parseFloat(b.estimated_saving["total"]?.amount || "0");
          return bSaving - aSaving; // Sort in descending order (highest savings first)
        });
    }
    return energySwitch.available_tariffs
      .filter(deal => 
        (selectedTariffType === "Fixed" && deal.tariff_type === "Fixed") ||
        (selectedTariffType === "Standard Variable" && deal.tariff_type === "Standard Variable")
      )
      .sort((a, b) => {
        const aSaving = parseFloat(a.estimated_saving["total"]?.amount || "0");
        const bSaving = parseFloat(b.estimated_saving["total"]?.amount || "0");
        return bSaving - aSaving; // Sort in descending order (highest savings first)
      });
  }, [energySwitch.available_tariffs, selectedTariffType]);

  // Best deal is always the first item in the sorted array
  const getBestDealId = useMemo(() => 
    filteredTariffs.length > 0 ? 0 : null, 
    [filteredTariffs]
  );

  const handleProceed = (switchTo: string) => {
    setLoadingDealId(switchTo);
    const currentPath = window.location.pathname;
    router.navigate({
      to: `${currentPath}/switching-info-form`,
      search: { switch_to: switchTo }
    });
    // Note: No need to reset loading state as we're navigating away
  };

  const openLightbox = (deal: Deal) => {
    setSelectedDeal(deal);
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  const openCalculationLightbox = () => {
    setIsCalculationLightboxOpen(true);
  };

  const closeCalculationLightbox = () => {
    setIsCalculationLightboxOpen(false);
  };

  const scrollToBestDeal = () => {
    if (bestDealRef.current) {
      bestDealRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const TrustpilotStars = ({ rating }: { rating: number | undefined }) => {
    if (rating === undefined) return <div className="flex items-center">Not rated</div>;
    
    const validRating = Math.min(Math.max(rating, 0), 5);
    const fullStars = Math.floor(validRating);
    const hasHalfStar = validRating % 1 >= 0.5;
    
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-5 h-5 ${
              i < fullStars
                ? 'text-green-500 fill-green-500'
                : i === fullStars && hasHalfStar
                ? 'text-green-500 fill-green-500 half-star'
                : 'text-gray-300'
            }`}
            aria-hidden={i >= fullStars && !(i === fullStars && hasHalfStar)}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{validRating.toFixed(1)}</span>
      </div>
    );
  };

  // Extracted reusable lightbox component
  const LightboxWrapper = ({ isOpen, onClose, children }: { 
    isOpen: boolean, 
    onClose: () => void, 
    children: React.ReactNode 
  }) => {
    return (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  // ImageWithFallback component for better image error handling
  const ImageWithFallback = ({ 
    src, 
    alt, 
    width, 
    height, 
    layout, 
    objectFit, 
    priority,
    className
  }: {
    src: string,
    alt: string,
    width?: number,
    height?: number,
    layout?: "fill" | "fixed" | "intrinsic" | "responsive",
    objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down",
    priority?: boolean,
    className?: string
  }) => {
    const [error, setError] = useState(false);

    if (error) {
      return (
        <div className={`flex items-center justify-center bg-gray-100 text-gray-500 ${className || 'w-full h-full'}`}>
          <span className="text-4xl font-bold">{alt.charAt(0).toUpperCase()}</span>
        </div>
      );
    }

    return (
      <Image 
        src={src} 
        alt={alt} 
        width={width} 
        height={height} 
        objectFit={objectFit} 
        priority={priority} 
        className={className}
        onError={() => setError(true)}
      />
    );
  };

  const Lightbox = ({ deal }: { deal: Deal }) => (
    <div className="bg-white rounded-lg p-6 w-full max-w-4xl h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-medium text-gray-800">{deal.supplier_name} - {deal.tariff_name}</h2>
        <Button variant="ghost" size="icon" onClick={closeLightbox} aria-label="Close details">
          <X className="h-6 w-6" />
        </Button>
      </div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="relative w-16 h-16 rounded-full overflow-hidden">
            <ImageWithFallback 
              src={deal.logo} 
              alt={`${deal.supplier_name} logo`} 
              layout="fill" 
              objectFit="cover" 
              priority 
            />
          </div>
          <div>
            <p className="text-lg font-medium text-gray-600">Estimated Annual Saving</p>
            <p className="text-3xl font-medium text-green-600">
              £{deal.estimated_saving['total']?.amount || 0} 
              {deal.estimated_saving['total']?.percentage ? 
                `(${deal.estimated_saving['total'].percentage}%)` : ''}
            </p>
          </div>
        </div>
        <Button 
          size="sm" 
          onClick={() => handleProceed(deal.id as string)} 
          className={`bg-[${THEME.primary}] hover:bg-[${THEME.primaryHover}] text-white font-medium`}
          disabled={loadingDealId === deal.id}
        >
          {loadingDealId === deal.id ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            "Switch"
          )}
        </Button>
      </div>
      <OfferDetailsTabs deal={deal} currentSupplier={energySwitch.current_supplier} TrustpilotStars={TrustpilotStars} />
    </div>
  );

  const CalculationLightbox = () => (
    <div className="bg-white rounded-lg p-8 w-full max-w-2xl h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-medium text-gray-800">How are these calculations done?</h2>
        <Button variant="ghost" size="icon" onClick={closeCalculationLightbox} aria-label="Close calculation info">
          <X className="h-6 w-6" />
        </Button>
      </div>
      <Tabs defaultValue="projected-annual-spend" className="space-y-8">
        <TabsList className="space-x-4">
          <TabsTrigger value="projected-annual-spend" className="px-4 py-2 text-sm font-medium rounded-full">Projected Annual Spend</TabsTrigger>
          {/* <TabsTrigger value="cost-of-current-tariff" className="px-4 py-2 text-sm font-medium rounded-full">Cost of Current Tariff</TabsTrigger> */}
          <TabsTrigger value="estimated-usage" className="px-4 py-2 text-sm font-medium rounded-full">Your Estimated Usage</TabsTrigger>
        </TabsList>
        <TabsContent value="projected-annual-spend" className="space-y-4">
          <p className="text-gray-600 font-medium">
            Your projected annual cost is an estimate of how much you'll spend on energy over the next 12 months if you choose not to switch. This calculation is based on your estimated annual energy usage and the most recent rates provided by your supplier for your current tariff, which are still based on the price cap for the period from 1 July to 30 September.
          </p>
          <p className="text-gray-600 font-medium">
            Please be aware that future price cap rates (e.g., from October onwards) cannot be included in our calculations until they are confirmed by Ofgem* and individual suppliers. As a result, any future energy usage will be calculated using the most recently confirmed price cap rates provided by your supplier.
          </p>
          <p className="text-gray-600 font-medium">
            *Ofgem has announced the October price cap rates, and from 1 October to 31 December, average energy bills for those on Standard Variable Tariffs (SVT) are expected to increase by 10%.
          </p>
        </TabsContent>
        <TabsContent value="cost-of-current-tariff">
          <Table className="w-full">
            <TableBody>
              <TableRow className="border-b">
                <TableCell className="py-3 text-left font-medium">Electricity standing charge</TableCell>
                <TableCell className="py-3 text-right">
                  {energySwitch.current_supplier['electricity'] ? 
                    `£${((energySwitch.current_supplier['electricity'].standing_charge || 0) * 365 / 100).toFixed(2)}` : 
                    '£0.00'}
                </TableCell>
              </TableRow>
              <TableRow className="border-b">
                <TableCell className="py-3 text-left font-medium">Electricity consumption</TableCell>
                <TableCell className="py-3 text-right">
                  {energySwitch.current_supplier['electricity'] && energySwitch.current_supplier['electricity'].estimated_yearly_cost ? 
                    `£${(energySwitch.current_supplier['electricity'].estimated_yearly_cost - ((energySwitch.current_supplier['electricity'].standing_charge || 0) * 365 / 100)).toFixed(2)}` : 
                    '£0.00'}
                </TableCell>
              </TableRow>
              <TableRow className="border-b">
                <TableCell className="py-3 text-left font-medium">Electricity taxes (5% VAT)</TableCell>
                <TableCell className="py-3 text-right">
                  {energySwitch.current_supplier['electricity'] && energySwitch.current_supplier['electricity'].estimated_yearly_cost ? 
                    `£${(energySwitch.current_supplier['electricity'].estimated_yearly_cost * 0.05).toFixed(2)}` : 
                    '£0.00'}
                </TableCell>
              </TableRow>
              <TableRow className="bg-blue-50">
                <TableCell className="py-3 text-left font-bold">Total cost</TableCell>
                <TableCell className="py-3 text-right font-bold">
                  {energySwitch.current_supplier['electricity'] && energySwitch.current_supplier['electricity'].formatted_estimated_cost ? 
                    energySwitch.current_supplier['electricity'].formatted_estimated_cost : 
                    '£0.00'}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TabsContent>
        <TabsContent value="estimated-usage">
          <p className="text-gray-600 font-medium">
            Your estimated annual consumption figures are provided by industry-standard sources used across the energy sector. While they are regularly updated, they should closely align with the estimated annual consumption shown in your energy statements from your supplier.
          </p>
        </TabsContent>
      </Tabs>
      <div className="mt-8">
        <a href="https://www.ofgem.gov.uk/energy-price-cap" target="_blank" rel="noopener noreferrer">
          <Button 
            size="sm" 
            className="bg-[#fe6232] hover:bg-[#fe6232]/90 text-white font-medium"
          >
            Ofgem Energy Price Cap
          </Button>
        </a>
      </div>
    </div>
  );

  const getFuelTypeBadge = (deal: Deal) => {
    const fuelTypes = Object.keys(deal.estimated_cost);
    if (fuelTypes.includes("gas") && fuelTypes.includes("electricity")) {
      return {
        text: "Gas & Electricity",
        tooltip: "This is a Gas & Electricity tariff."
      };
    } else if (fuelTypes.includes("gas")) {
      return {
        text: "Gas Only",
        tooltip: "This is a Gas Only tariff."
      };
    } else if (fuelTypes.includes("electricity")) {
      return {
        text: "Electricity Only",
        tooltip: "This is an Electricity Only tariff."
      };
    }
    return null;
  };

  const getUnitRateLabel = (deal: Deal, selectedFuelType: string) => {
    if (Object.keys(deal.estimated_cost).length > 1) {
      return `${selectedFuelType.charAt(0).toUpperCase() + selectedFuelType.slice(1)} Unit Rate`;
    }
    return deal.gas ? 'Gas Unit Rate' : 'Electricity Unit Rate';
  };

  const getStandingChargeLabel = (deal: Deal, selectedFuelType: string) => {
    if (Object.keys(deal.estimated_cost).length > 1) {
      return `${selectedFuelType.charAt(0).toUpperCase() + selectedFuelType.slice(1)} Standing Charge`;
    }
    return deal.gas ? 'Gas Standing Charge' : 'Electricity Standing Charge';
  };

  // Card component for displaying similar info across the UI
  const InfoCard = ({ title, value, className }: { title: string, value: string | number | undefined, className?: string }) => (
    <div className={`bg-gray-50 p-4 rounded-lg ${className}`}>
      <h3 className="text-xs font-medium text-gray-500 mb-1">{title}</h3>
      <p className="text-2xl font-medium text-gray-800">{value || '-'}</p>
    </div>
  );

  // Exit fee display component for standardization
  const ExitFeeDisplay = ({ exitFee, isCompact = false }: { exitFee: number, isCompact?: boolean }) => (
    <div className={isCompact ? "mt-3 text-xs text-gray-500 italic flex items-center" : ""}>
      {isCompact ? <Info className="h-3 w-3 mr-1" /> : null}
      <span>{isCompact ? "Early Exit Fee: " : ""}£{exitFee}</span>
    </div>
  );

  return (
    <div className="font-sans">
      <div className="bg-[#FAFAFA] pb-0">
        <div className="max-w-[1200px] mx-auto px-4 py-8">
          <Header progressPercentage={50} />

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <Card className="mb-12 overflow-hidden">
              <div className="bg-[#fe6232] text-white py-2 px-4 flex justify-between items-center">
                <h3 className="text-md font-medium">Your current energy supplier</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={openCalculationLightbox} 
                  className="text-white hover:text-[#fe6232] transition-colors font-medium"
                >
                  How are these calculations done?
                </Button>
              </div>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div>
                    <h2 className="text-2xl font-medium flex items-center text-gray-800">
                      <Mail className="mr-2 h-5 w-5 text-gray-500" />
                      {energySwitch.switch_user.email}
                    </h2>
                    <div className="flex space-x-4 text-sm text-gray-500 mt-1 font-medium">
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        {energySwitch.address.full_address}
                      </span>
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        {energySwitch.current_supplier.supplier_name}
                      </span>

                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        {energySwitch.current_supplier?.profile_class == 1 ? "Domestic Unrestricted" : "Domestic Economy 7"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-start space-x-4 mb-4">
                  {
                    energySwitch.current_supplier['gas'] && (
                      <Button 
                        variant={selectedFuelType === "gas" ? "default" : "outline"} 
                        onClick={() => setSelectedFuelType("gas")}
                      >
                        Gas
                      </Button>
                    )
                  }
                  {
                    energySwitch.current_supplier['electricity'] && (
                      <Button
                        variant={selectedFuelType === "electricity" ? "default" : "outline"} 
                        onClick={() => setSelectedFuelType("electricity")}
                      >
                        Electricity
                      </Button>
                    )
                  }
                </div>
                <div className="min-h-[200px]">
                  {
                    energySwitch.current_supplier['gas'] && (selectedFuelType === "gas" || energySwitch.current_supplier['electricity'] === null) && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <InfoCard title="Gas Unit Rate" value={`${energySwitch.current_supplier['gas'].unit_rate} p/kWh`} />
                        <InfoCard title="Gas Standing Charge" value={`${energySwitch.current_supplier['gas'].standing_charge} p/day`} />
                        <InfoCard title="Meter Serial Number" value={energySwitch.current_supplier['gas'].meter_serial_number} />
                        <InfoCard title="Monthly Usage" value={`${energySwitch.current_supplier['gas'].monthly_usage} kWh`} />
                        <InfoCard title="Estimated Cost" value={energySwitch.current_supplier['gas'].formatted_estimated_cost} />
                        <InfoCard title="MPRN" value={energySwitch.current_supplier['gas'].meter_point_reference_number} />
                      </div>
                    )
                  }
                  {
                    energySwitch.current_supplier['electricity'] && (selectedFuelType === "electricity" || energySwitch.current_supplier['gas'] === null) && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <InfoCard title="Electricity Unit Rate" value={`${energySwitch.current_supplier['electricity'].unit_rate} p/kWh`} />
                        <InfoCard title="Electricity Standing Charge" value={`${energySwitch.current_supplier['electricity'].standing_charge} p/day`} />
                        <InfoCard title="Meter Serial Number" value={energySwitch.current_supplier['electricity'].meter_serial_number} />
                        <InfoCard title="Monthly Usage" value={`${energySwitch.current_supplier['electricity'].monthly_usage} kWh`} />
                        <InfoCard title="Estimated Cost" value={energySwitch.current_supplier['electricity'].formatted_estimated_cost} />
                        <InfoCard title="MPAN" value={energySwitch.current_supplier['electricity'].mpan} />
                      </div>
                    )
                  }
                </div>

                {energySwitch.current_supplier.gas && energySwitch.current_supplier.electricity && (
                  <div className="mt-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-sm font-medium text-gray-600 mb-1">Total Estimated Annual Cost</h3>
                    <p className="text-2xl font-bold text-orange-500">
                      £{((energySwitch.current_supplier['electricity'].estimated_costs?.[0] || 0) + 
                         (energySwitch.current_supplier['gas'].estimated_costs?.[0] || 0)).toFixed(2)}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>

      <div className="max-w-[1200px] mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <div className="flex justify-between items-center mb-6">
            {filteredTariffs.length > 0 ? (
              <h2 className="text-2xl font-bold text-gray-800">
                Save up to <span 
                  className="text-orange-500 cursor-pointer hover:underline" 
                  onClick={scrollToBestDeal}
                  title="Click to see the best deal"
                >£{useMemo(() => {
                  const maxSaving = Math.max(...filteredTariffs.map(deal => 
                    parseFloat(deal.estimated_saving["total"]?.amount || "0")
                  ));
                  return maxSaving.toFixed(0);
                }, [filteredTariffs])}</span> by making a switch today
              </h2>
            ) : (
              <div></div>
            )}
            
            <div className="flex items-center space-x-4">
              <Select onValueChange={setSelectedTariffType} defaultValue="all">
                <SelectTrigger className="w-[200px] focus:ring-0 focus:ring-offset-0 focus:border-gray-300">
                  <SelectValue placeholder="Filter Energy Tariffs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tariffs</SelectItem>
                  <SelectItem value="Fixed">Fixed</SelectItem>
                  <SelectItem value="Standard Variable">Variable</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {filteredTariffs.length === 0 ? (
            <Card className="w-full mb-8 p-8 text-center bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <CardHeader>
                <CardTitle className="text-3xl font-bold text-gray-800 mb-2">Oops! No Tariffs Found Right Now</CardTitle>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  We've searched high and low, but couldn't find any tariffs for you at the moment. Don't worry, we're always on the lookout for great energy deals, so be sure to check back soon!
                </p>
              </CardHeader>
              <CardContent>
                <h3 className="text-xl font-semibold text-gray-700 mb-6">In the meantime, feel free to:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                  <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg">
                    <Upload className="w-12 h-12 text-orange-500 mb-4 mx-auto" aria-hidden="true" />
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Upload Another Bill</h4>
                    <p className="text-gray-600 mb-4">We'll give it another go and see if we can find some great deals for you!</p>
                    <Button className="bg-orange-500 hover:bg-orange-600 text-white font-medium w-full">
                      Upload Bill
                    </Button>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg">
                    <PhoneCall className="w-12 h-12 text-green-500 mb-4 mx-auto" aria-hidden="true" />
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Contact Support</h4>
                    <p className="text-gray-600 mb-4">Our friendly team is here to help. Reach out if you have any questions.</p>
                    <Button variant="outline" className="text-green-600 border-green-600 hover:bg-green-50 font-medium w-full">
                      Get in Touch
                    </Button>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg">
                    <Lightbulb className="w-12 h-12 text-yellow-500 mb-4 mx-auto" aria-hidden="true" />
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Energy-Saving Tips</h4>
                    <p className="text-gray-600 mb-4">Explore ways to save energy and reduce your bills while you wait. 🌱</p>
                    <Button variant="outline" className="text-yellow-600 border-yellow-600 hover:bg-yellow-50 font-medium w-full">
                      View Tips
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            // Existing code for rendering tariffs
            filteredTariffs.map((deal, index) => {
              const dealFuelTypes = Object.keys(deal.estimated_cost) as ("gas" | "electricity")[];
              const showBothFuelTypes = dealFuelTypes.includes("gas") && dealFuelTypes.includes("electricity");
              const dealFuelType = dealFuelTypes.includes(selectedFuelType) ? selectedFuelType : dealFuelTypes[0];

              return (
                <Card 
                  key={index} 
                  className="w-full mb-8 flex overflow-hidden hover:shadow-lg transition-shadow duration-500"
                  ref={index === getBestDealId ? bestDealRef : undefined}
                >
                  <div className="w-1/3 relative flex items-center justify-center p-4 overflow-hidden">
                    <div className="absolute inset-0 bg-cover bg-center blur-md opacity-50" style={{ backgroundImage: `url(${deal.logo})` }} aria-hidden="true"></div>
                    <div className="relative z-10 w-40 h-40 rounded-full overflow-hidden bg-white shadow-lg border-4 border-white transition-transform duration-300 hover:scale-105">
                      <ImageWithFallback
                        priority
                        src={deal.logo}
                        alt={`${deal.supplier_name} Logo`}
                        layout="fill"
                        objectFit="contain"
                        className="p-2"
                      />
                    </div>
                    {deal.estimated_saving[dealFuelType] && (
                      <div 
                        className="absolute top-2 left-2 bg-green-500 text-white text-xs font-medium px-2 py-1 rounded-full cursor-help transition-all duration-500 hover:bg-green-600"
                        title="This is your annual saving compared to your current energy supplier tariff."
                      >
                        {deal.estimated_saving["total"].percentage}% annual saving
                      </div>
                    )}
                    <div className="absolute bottom-2 left-2 flex items-center space-x-2">
                      {(() => {
                        const badge = getFuelTypeBadge(deal);
                        if (badge) {
                          return (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center space-x-1">
                                    <span className="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                                      {badge.text}
                                    </span>
                                    <Info className="w-4 h-4 text-blue-500" aria-hidden="true" />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{badge.tooltip}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </div>
                  <div className="flex-1 flex flex-col">
                    <CardContent className="p-6 flex-grow">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h2 className="text-2xl font-medium text-gray-800 mb-1">{deal.supplier_name}</h2>
                          <p className="text-base font-medium text-gray-600">{deal.tariff_name}</p>
                        </div>
                        <div 
                          className="text-sm px-3 py-1.5 font-semibold rounded-md bg-[#fe6232]/10 text-[#fe6232] border border-[#fe6232]/20 cursor-help"
                          title={
                            deal.tariff_type === "Fixed" 
                              ? "This is a fixed rate tariff for 12-months." 
                              : deal.tariff_type === "Standard Variable" 
                                ? "This is a Standard Variable Tariff with no expiry date."
                                : undefined
                          }
                        >
                          {deal.tariff_type === "Fixed" 
                            ? "12-Months Fixed" 
                            : deal.tariff_type === "Standard Variable"
                              ? "Standard Variable Tariff"
                              : deal.tariff_type
                          }
                        </div>
                      </div>

                      {showBothFuelTypes ? (
                        <div className="mb-4">
                          {/* Gas Section */}
                          {dealFuelTypes.includes("gas") && (
                            <div className="mb-4">
                              <h3 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                Gas Details
                              </h3>
                              <div className="grid grid-cols-2 gap-4 bg-gray-50 p-3 rounded-lg">
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Gas Unit Rate</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    {deal["gas"]?.unit_rate || '-'} p/kWh
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Gas Standing Charge</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    {deal["gas"]?.standing_charge || '-'} p/day
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Estimated Monthly Cost</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    £{deal.estimated_cost["gas"]?.monthly || '-'}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Estimated Saving</p>
                                  <p className="text-lg font-medium text-green-600">
                                    £{deal.estimated_saving["gas"]?.amount || '0'} 
                                    {deal.estimated_saving["gas"]?.percentage && deal.estimated_saving["gas"].percentage !== '0' ? 
                                      `(${deal.estimated_saving["gas"].percentage}%)` : ''}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Electricity Section */}
                          {dealFuelTypes.includes("electricity") && (
                            <div>
                              <h3 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                <Lightbulb className="h-4 w-4 mr-1" aria-hidden="true" />
                                Electricity Details
                              </h3>
                              <div className="grid grid-cols-2 gap-4 bg-gray-50 p-3 rounded-lg">
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Electricity Unit Rate</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    {deal["electricity"]?.unit_rate || '-'} p/kWh
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Electricity Standing Charge</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    {deal["electricity"]?.standing_charge || '-'} p/day
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Estimated Monthly Cost</p>
                                  <p className="text-lg font-medium text-gray-800">
                                    £{deal.estimated_cost["electricity"]?.monthly || '-'}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-gray-500">Estimated Saving</p>
                                  <p className="text-lg font-medium text-green-600">
                                    £{deal.estimated_saving["electricity"]?.amount || '-'} 
                                    {deal.estimated_saving["electricity"]?.percentage && deal.estimated_saving["electricity"].percentage !== '0' ? 
                                      `(${deal.estimated_saving["electricity"].percentage}%)` : ''}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-xs font-medium text-gray-500">{getUnitRateLabel(deal, dealFuelType)}</p>
                            <p className="text-lg font-medium text-gray-800">
                              {deal[dealFuelType]?.unit_rate || '-'} p/kWh
                            </p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500">{getStandingChargeLabel(deal, dealFuelType)}</p>
                            <p className="text-lg font-medium text-gray-800">
                              {deal[dealFuelType]?.standing_charge || '-'} p/day
                            </p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500">Early Exit Fee</p>
                            <p className="text-lg font-medium text-gray-800">£{deal.exit_fee}</p>
                          </div>
                          <div>
                            <p className="text-xs font-medium text-gray-500">Estimated Monthly Cost</p>
                            <p className="text-lg font-medium text-gray-800">
                              £{deal.estimated_cost[dealFuelType]?.monthly || '-'}
                            </p>
                          </div>
                        </div>
                      )}

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-xs font-medium text-gray-500">Estimated Yearly Cost</p>
                            <p className="text-xl font-medium text-gray-800">
                              {showBothFuelTypes ? (
                                <>£{(
                                  parseFloat(deal.estimated_cost["gas"]?.yearly || "0") + 
                                  parseFloat(deal.estimated_cost["electricity"]?.yearly || "0")
                                ).toFixed(2)}</>
                              ) : (
                                <>£{deal.estimated_cost[dealFuelType]?.yearly || '-'}</>
                              )}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-xs font-medium text-gray-500">Estimated Saving</p>
                            <p className="text-xl font-medium text-green-600">
                              {showBothFuelTypes ? (
                                <>£{deal.estimated_saving["total"]?.amount || 0} 
                                {deal.estimated_saving["total"]?.percentage && deal.estimated_saving["total"].percentage !== '0' ?
                                  `(${deal.estimated_saving["total"].percentage}%)` : ''
                                }</>
                              ) : (
                                <>£{deal.estimated_saving[dealFuelType]?.amount || '-'} 
                                {deal.estimated_saving[dealFuelType]?.percentage && deal.estimated_saving[dealFuelType].percentage !== '0' ?
                                  `(${deal.estimated_saving[dealFuelType].percentage}%)` : ''
                                }</>
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      {showBothFuelTypes && <ExitFeeDisplay exitFee={deal.exit_fee} isCompact={true} />}
                    </CardContent>
                    <CardFooter className="p-6 pt-0 flex justify-end space-x-4">
                      <Button variant="outline" size="sm" className="font-medium" onClick={() => openLightbox(deal)}>Tariff Details</Button>
                      <Button 
                        size="sm" 
                        onClick={() => handleProceed(deal.id as string)} 
                        className="bg-[#fe6232] hover:bg-[#fe6232]/90 text-white font-medium"
                        disabled={loadingDealId === deal.id}
                      >
                        {loadingDealId === deal.id ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          "Switch"
                        )}
                      </Button>
                    </CardFooter>
                  </div>
                </Card>
              );
            })
          )}
        </motion.div>

        <Footer />
      </div>

      <AnimatePresence>
        {isLightboxOpen && selectedDeal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <Lightbox deal={selectedDeal} />
          </motion.div>
        )}
        
        {isCalculationLightboxOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <CalculationLightbox />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}