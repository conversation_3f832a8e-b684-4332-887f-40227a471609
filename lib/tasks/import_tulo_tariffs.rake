require 'csv'

namespace :import do
  desc "Import Tulo Energy tariffs from Tulo_Tariffs_April_2025.csv"
  task tulo_tariffs: :environment do
    puts "Starting Tulo Energy tariff import..."

    csv_file_path = Rails.root.join('Tulo_Tariffs_April_2025.csv')
    unless File.exist?(csv_file_path)
      puts "Error: CSV file not found at #{csv_file_path}"
      exit
    end

    # --- Supplier Setup ---
    supplier_name = "Tulo Energy"
    # Using Rebel logo as placeholder, replace if a Tulo logo is available
    supplier_logo_url = "https://grtfqhhlhjskzdbufmck.supabase.co/storage/v1/object/public/assets/suppliers/tulo-logo.jpg"
    supplier_about = "Tulo Energy tariffs effective from April 2025." # Basic description
    supplier = Supplier.find_or_create_by!(name: supplier_name) do |s|
      s.logo = supplier_logo_url
      s.about = supplier_about
      # trustpilot_rating can be added later if known
    end
    puts "Found or created Supplier: #{supplier.name}"

    # --- Tariff Defaults ---
    effective_date = Date.new(2025, 4, 1)
    default_duration = 12 # months
    default_exit_fees = 0
    default_payment_methods = [PaymentMethod::MONTHLY_VARIABLE_DIRECT_DEBIT]

    # --- CSV Parsing Logic ---
    current_tariff_type = nil
    current_tariff = nil
    headers = []

    CSV.foreach(csv_file_path, headers: false) do |row|
      next if row.empty? || row.all?(&:nil?) # Skip empty rows

      # Check for table headers
      if row[0]&.start_with?('Tulo Vari-One:')
        current_tariff_type = :fixed
        tariff_name = "Tulo Vari-One"
        puts "\nProcessing #{tariff_name}..."

        # Create or find the fixed tariff
        current_tariff = supplier.energy_tariffs.find_or_initialize_by(
          tariff_name: tariff_name,
          energy_type: :both,
          tariff_type: :fixed
        )

        # Clear existing rates if tariff already exists
        if current_tariff.persisted?
          puts "Clearing existing rates for #{tariff_name}..."
          current_tariff.energy_tariff_rates.destroy_all
        end

        current_tariff.assign_attributes(
          display_name: tariff_name,
          effective_from: effective_date,
          duration: default_duration,
          exit_fees: default_exit_fees,
          payment_methods: default_payment_methods,
          supplier_id: supplier.id # Ensure supplier_id is set
        )
        current_tariff.save!
        puts "Created/Updated EnergyTariff: #{current_tariff.tariff_name} (ID: #{current_tariff.id})"

        next # Skip the table title row
      elsif row[0]&.start_with?('Tulo SVT:')
        current_tariff_type = :variable
        tariff_name = "Tulo SVT"
        puts "\nProcessing #{tariff_name}..."

        # Create or find the variable tariff
        current_tariff = supplier.energy_tariffs.find_or_initialize_by(
          tariff_name: tariff_name,
          energy_type: :both,
          tariff_type: :variable
        )

        # Clear existing rates if tariff already exists
        if current_tariff.persisted?
          puts "Clearing existing rates for #{tariff_name}..."
          current_tariff.energy_tariff_rates.destroy_all
        end

        current_tariff.assign_attributes(
          display_name: tariff_name,
          effective_from: effective_date,
          duration: default_duration,
          exit_fees: default_exit_fees,
          payment_methods: default_payment_methods,
          supplier_id: supplier.id # Ensure supplier_id is set
        )
        current_tariff.save!
        puts "Created/Updated EnergyTariff: #{current_tariff.tariff_name} (ID: #{current_tariff.id})"

        next # Skip the table title row
      end

      # Skip processing until a tariff is created
      next unless current_tariff

      # Check for CSV header row
      if row.include?('GSP') && row.include?('GASSTANDINGCHARGERATE')
        headers = row.map { |h| h&.strip }
        puts "Found headers: #{headers.join(', ')}"
        next # Skip the header row itself
      end

      # Process data rows if headers are present
      next if headers.empty?

      # --- Create EnergyTariffRate records ---
      data = headers.zip(row).to_h
      gsp_code = data['GSP']

      # 1. Gas Rate
      if data['GASSTANDINGCHARGERATE'].present? && data['GASCONSUMPTIONRATE'].present?
        EnergyTariffRate.create!(
          energy_tariff: current_tariff,
          fuel_type: 'gas',
          profile_class: -99, # N/A for gas
          gsp_code: gsp_code,
          standing_charge_inc_vat: BigDecimal(data['GASSTANDINGCHARGERATE']),
          unit_rate_inc_vat: BigDecimal(data['GASCONSUMPTIONRATE'])
          # Storing inclusive VAT rates as requested
        )
        # puts "  - Created Gas Rate (Inc VAT)"
      end

      # 2. Electricity Standard Rate (Profile Class 1)
      if data['ELECSTANDINGCHARGERATE'].present? && data['ELECALLUNITS'].present?
        EnergyTariffRate.create!(
          energy_tariff: current_tariff,
          fuel_type: 'electricity',
          profile_class: 1,
          gsp_code: gsp_code,
          standing_charge_inc_vat: BigDecimal(data['ELECSTANDINGCHARGERATE']),
          unit_rate_inc_vat: BigDecimal(data['ELECALLUNITS'])
          # Storing inclusive VAT rates as requested
        )
        puts "  - Created Electricity Standard Rate (PC1, Inc VAT) for GSP: #{gsp_code}"
      end

      # 3. Electricity Economy 7 Rate (Profile Class 2)
      if data['ELECSTANDINGCHARGERATEMULTI'].present? && data['ELECDAYUNITS'].present? && data['ELECNIGHTUNITS'].present?
        EnergyTariffRate.create!(
          energy_tariff: current_tariff,
          fuel_type: 'electricity',
          profile_class: 2,
          gsp_code: gsp_code,
          standing_charge_inc_vat: BigDecimal(data['ELECSTANDINGCHARGERATEMULTI']), # E7 standing charge
          day_unit_rate_inc_vat: BigDecimal(data['ELECDAYUNITS']),
          night_unit_rate_inc_vat: BigDecimal(data['ELECNIGHTUNITS'])
          # Storing inclusive VAT rates as requested
        )
        puts "  - Created Electricity Economy 7 Rate (PC2, Inc VAT) for GSP: #{gsp_code}"
      end

    end

    puts "\nFinished Tulo Energy tariff import."
  rescue => e
    puts "\nAn error occurred during import: #{e.message}"
    puts e.backtrace.join("\n")
  end
end
