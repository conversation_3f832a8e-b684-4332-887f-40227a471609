import useSWR, { useSWRConfig } from 'swr';
import { EnergySwitch, SwitchStatusType } from '@/types/types';
import { useSession } from 'next-auth/react';
import useSWRMutation from 'swr/mutation';

/**
 * Base fetcher function that handles authentication with timeout
 */
const apiFetcher = (token: string) => async (url: string) => {
  // Create an AbortController for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = new Error('An error occurred while fetching the data.');
      error.message = `${response.status}: ${response.statusText}`;
      throw error;
    }

    return response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('Request timeout - please check your connection and try again');
    }
    throw error;
  }
};

/**
 * Helper function to get access token from session
 */
export function getAccessToken(session: any): string | null {
  if (!session) return null;
  
  // Get token from session - checking both potential locations
  return session?.user?.accessToken || session?.accessToken || null;
}

/**
 * Hook to fetch energy switch data with SWR
 */
export function useEnergySwitch(switchId: string | null) {
  const { data: session } = useSession();
  const token = getAccessToken(session);
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  const { data, error, isLoading, mutate } = useSWR(
    switchId && token 
      ? `${baseUrl}/api/v1/energy_switches/${switchId}/tariff_comparison`
      : null,
    token ? apiFetcher(token) : null
  );
  
  return {
    energySwitch: data as EnergySwitch,
    isLoading,
    isError: error,
    mutate
  };
}

/**
 * Hook to fetch energy switch status with SWR
 */
export function useEnergySwitchStatus(switchId: string | null) {
  const { data: session } = useSession();
  const token = getAccessToken(session);
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  const { data, error, isLoading, mutate } = useSWR(
    switchId && token 
      ? `${baseUrl}/api/v1/energy_switches/${switchId}/switch_status`
      : null,
    token ? apiFetcher(token) : null
  );
  
  return {
    status: data as SwitchStatusType,
    isLoading,
    isError: error,
    mutate
  };
}

/**
 * Hook for confirming an energy switch using SWR
 * 
 * Example usage:
 * ```
 * // In your component:
 * const { trigger, isMutating, error } = useConfirmEnergySwitch(switchId);
 * 
 * // When user submits the form:
 * const handleSubmit = async (formData) => {
 *   try {
 *     const result = await trigger({ 
 *       switchTo: selectedTariff, 
 *       formData: formData 
 *     });
 *     // Handle success
 *   } catch (error) {
 *     // Handle error
 *   }
 * };
 * ```
 */
export function useConfirmEnergySwitch(switchId: string | null) {
  const { data: session } = useSession();
  const token = getAccessToken(session);
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  const url = switchId && token 
    ? `${baseUrl}/api/v1/energy_switches/${switchId}/confirm_switch`
    : null;

  // Define the fetcher function for the mutation
  const confirmSwitchFetcher = async (
    url: string,
    { arg }: { arg: { switchTo: string; formData: any } }
  ) => {
    const { switchTo, formData } = arg;
    
    const requestBody = {
      ...formData,
      switch_to: switchTo
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error('Failed to confirm switch');
    }

    return response.json();
  };

  return useSWRMutation(url, confirmSwitchFetcher);
}


/**
 * Local storage functions for switch ID
 * 
 * Note: These functions are deprecated and should not be used.
 * The energy switch ID should be obtained from the authenticated session
 * via session.user.energySwitchId
 */
export function storeVerifiedSwitchId(id: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('verifiedSwitchId', id);
  }
}

export function getVerifiedSwitchId(): string | null {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('verifiedSwitchId');
  }
  return null;
} 